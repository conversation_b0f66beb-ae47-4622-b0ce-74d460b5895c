"use client";

import { useState, useEffect, useCallback } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/app-components/dataTable";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Loader2, Trash2, Star } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import Image from "next/image";
import { Testimonial } from "@/lib/types";
import {
<<<<<<< HEAD
  getTestimonials,
  updateTestimonialStatus,
  deleteTestimonial,
} from "@/services/testimonialApi";
import Pagination from "@/app-components/pagination";
import ConfirmDialog from "@/app-components/ConfirmDialog";
=======
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Testimonial } from '@/lib/types';
import { getTestimonials, updateTestimonialStatus, deleteTestimonial } from '@/services/testimonialApi';
import { usePermissions } from '@/lib/usePermissions';
>>>>>>> 044ecf2 (Update: add functionally of roles and permission)

const PAGE_SIZE = 10;

const TestimonialTable = () => {
  const { hasPermission, isLoading: permissionsLoading } = usePermissions();
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isDeleting, setIsDeleting] = useState(false);
  const [updatingTestimonialId, setUpdatingTestimonialId] = useState<
    string | null
  >(null);
  const [testimonialToDelete, setTestimonialToDelete] = useState<string | null>(
    null
  );
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<
    "ALL" | "PENDING" | "APPROVED" | "REJECTED"
  >("ALL");

<<<<<<< HEAD
  const fetchTestimonials = useCallback(
    async (
      page: number,
      status?: "PENDING" | "APPROVED" | "REJECTED" | "ALL"
    ) => {
      try {
        setIsLoading(true);
        const filterStatus =
          status && status !== "ALL"
            ? (status as "PENDING" | "APPROVED" | "REJECTED")
            : undefined;
        const response = await getTestimonials(page, PAGE_SIZE, filterStatus);
=======
  // Permission checks
  const canUpdateTestimonials = hasPermission('update testimonials');
  const canDeleteTestimonials = hasPermission('delete testimonials');

  const fetchTestimonials = useCallback(async (page: number, status?: 'PENDING' | 'APPROVED' | 'REJECTED' | 'ALL') => {
    try {
      setIsLoading(true);
      const filterStatus = status && status !== 'ALL' ? status as 'PENDING' | 'APPROVED' | 'REJECTED' : undefined;
      const response = await getTestimonials(page, PAGE_SIZE, filterStatus);
>>>>>>> 044ecf2 (Update: add functionally of roles and permission)

        setTestimonials(response.testimonials || []);
        setTotalPages(response.pages || 1);
      } catch (error: any) {
        toast.error(error.message || "Failed to fetch testimonials");
        setTestimonials([]);
        setTotalPages(1);
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    fetchTestimonials(1);
  }, [fetchTestimonials]);

  // Show loading state while permissions are being fetched
  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <span>Loading permissions...</span>
      </div>
    );
  }

  // Check if user has permission to view testimonials
  if (!hasPermission('read testimonials')) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You don&apos;t have permission to view testimonials.</p>
        </div>
      </div>
    );
  }

  const handleFilter = () => {
    setCurrentPage(1);
    fetchTestimonials(1, statusFilter);
  };

<<<<<<< HEAD
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      fetchTestimonials(page, statusFilter);
    }
  };

  const handleStatusChange = async (
    testimonialId: string,
    status: "PENDING" | "APPROVED" | "REJECTED"
  ) => {
=======
  const handleStatusChange = async (testimonialId: string, status: 'PENDING' | 'APPROVED' | 'REJECTED') => {
    if (!canUpdateTestimonials) {
      toast.error('You do not have permission to update testimonial status');
      return;
    }

>>>>>>> 044ecf2 (Update: add functionally of roles and permission)
    const previousTestimonials = [...testimonials];

    try {
      setUpdatingTestimonialId(testimonialId);

      setTestimonials(
        testimonials.map((t) =>
          t.id === testimonialId
            ? { ...t, status, updatedAt: new Date().toISOString() }
            : t
        )
      );

      const updatedTestimonial = await updateTestimonialStatus(
        testimonialId,
        status
      );

      if (updatedTestimonial) {
        setTestimonials(
          testimonials.map((t) =>
            t.id === testimonialId
              ? { ...updatedTestimonial, class: t.class }
              : t
          )
        );
        toast.success(`Testimonial status updated to ${status}`);
      }
    } catch (error: any) {
      console.error("Error updating testimonial status:", error);
      setTestimonials(previousTestimonials);
      toast.error(error.message || "Failed to update testimonial status");
    } finally {
      setUpdatingTestimonialId(null);
    }
  };

  const openDeleteConfirmation = (id: string) => {
    setTestimonialToDelete(id);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteTestimonial = async () => {
    if (!testimonialToDelete) return;

<<<<<<< HEAD
    setIsDeleting(true); // Start loader
=======
    if (!canDeleteTestimonials) {
      toast.error('You do not have permission to delete testimonials');
      return;
    }
>>>>>>> 044ecf2 (Update: add functionally of roles and permission)

    try {
      await deleteTestimonial(testimonialToDelete);
      toast.success("Testimonial deleted successfully!");
      fetchTestimonials(currentPage);
    } catch (error: any) {
      toast.error(error.message || "Failed to delete testimonial");
    } finally {
      setIsDeleting(false); // Stop loader
      setIsDeleteDialogOpen(false);
      setTestimonialToDelete(null);
    }
  };

  const renderStars = (rating: number) => (
    <div className="flex gap-1">
      {[...Array(5)].map((_, i) => (
        <Star
          key={i}
          className={`w-4 h-4 ${
            i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
          }`}
        />
      ))}
    </div>
  );

  const columns: ColumnDef<Testimonial>[] = [
    {
      accessorKey: "class",
      header: "Class",
      cell: ({ row }) => {
        const classData = row.original.class;
        const logo = classData.classesLogo || classData.ClassAbout?.classesLogo;
        const apiBaseUrl =
          process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:4005";

        return (
          <div className="flex items-center gap-3">
            {logo ? (
              <div className="relative w-10 h-10 rounded-full overflow-hidden border">
                <Image
                  src={`${apiBaseUrl}${logo}`}
                  alt="Logo"
                  fill
                  className="object-cover"
                />
              </div>
            ) : (
              <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                {classData.firstName?.[0] || "C"}
              </div>
            )}
            <div>
              <div className="font-medium">{classData.className || "N/A"}</div>
              <div className="text-xs text-gray-500">
                {classData.fullName ||
                  `${classData.firstName} ${classData.lastName}`}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "rating",
      header: "Rating",
      cell: ({ row }) => renderStars(row.original.rating),
    },
    {
      accessorKey: "message",
      header: "Testimonial",
      cell: ({ row }) => {
        const msg = row.original.message;
        const isLong = msg.length > 80;
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="cursor-pointer max-w-md">
                  {isLong ? `${msg.slice(0, 50)}...` : msg}
                </span>
              </TooltipTrigger>
              <TooltipContent
                className="max-w-[300px] p-3 bg-slate-200 text-black rounded-lg shadow-md border border-gray-600 text-sm leading-relaxed whitespace-normal break-words"
                side="bottom"
              >
                <p>{msg}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <Select
          value={row.original.status}
          onValueChange={(value: "PENDING" | "APPROVED" | "REJECTED") =>
            handleStatusChange(row.original.id, value)
          }
          disabled={updatingTestimonialId === row.original.id || !canUpdateTestimonials}
        >
          <SelectTrigger
            className="w-[150px] mb-3"
            aria-label={`Status for testimonial ${row.original.id}`}
          >
            {updatingTestimonialId === row.original.id ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <SelectValue placeholder="Select status" />
            )}
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="PENDING">Pending</SelectItem>
            <SelectItem value="APPROVED">Approved</SelectItem>
            <SelectItem value="REJECTED">Rejected</SelectItem>
          </SelectContent>
        </Select>
      ),
    },
    {
      accessorKey: "createdAt",
      header: "Submitted At",
      cell: ({ row }) => new Date(row.original.createdAt).toLocaleString(),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
<<<<<<< HEAD
        <Button
          variant="ghost"
          size="icon"
          onClick={() => openDeleteConfirmation(row.original.id)}
          className="text-red-500 hover:text-red-700 hover:bg-red-100"
        >
          <Trash2 className="h-4 w-4" />
          <span className="sr-only">Delete</span>
        </Button>
      ),
    },
=======
        <>
          {canDeleteTestimonials && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => openDeleteConfirmation(row.original.id)}
              className="text-red-500 hover:text-red-700 hover:bg-red-100"
            >
              <Trash2 className="h-4 w-4" />
              <span className="sr-only">Delete</span>
            </Button>
          )}
        </>
      )
    }
>>>>>>> 044ecf2 (Update: add functionally of roles and permission)
  ];

  return (
    <div>
      <div className="flex justify-between items-center mx-2 mb-2">
        <h1 className="text-2xl font-bold ms-2">Classes Testimonials</h1>
        <div className="flex gap-4">
          <Select
            value={statusFilter}
            onValueChange={(
              value: "ALL" | "PENDING" | "APPROVED" | "REJECTED"
            ) => setStatusFilter(value)}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="APPROVED">Approved</SelectItem>
              <SelectItem value="REJECTED">Rejected</SelectItem>
            </SelectContent>
          </Select>
          <div className="mt-1 flex items-center space-x-5">
            <Button onClick={handleFilter}>Filter</Button>
          </div>
        </div>
      </div>

      <DataTable
        columns={columns}
        data={testimonials}
        isLoading={isLoading}
      />

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={handlePageChange}
        entriesText={`${testimonials.length} entries`}
      />

      <ConfirmDialog
        open={isDeleteDialogOpen}
        setOpen={setIsDeleteDialogOpen}
        title="Are you sure?"
        description="This will permanently delete the testimonial."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteTestimonial}
        isLoading={isDeleting}
      />
    </div>
  );
};

export default TestimonialTable;
