'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Users, Download, Pencil, Trash2 } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogFooter,
} from '@/components/ui/alert-dialog';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/app-components/dataTable';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { usePermissions } from '@/lib/usePermissions';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
}

interface Role {
  id: string;
  name: string;
}

const API_BASE_URL = 'http://localhost:4005/api/v1';
const PAGE_SIZE = 10;

const AddUser = () => {
  const { hasPermission, isLoading: permissionsLoading } = usePermissions();
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [deletingUserId, setDeletingUserId] = useState<string | null>(null);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [editUserId, setEditUserId] = useState<string | null>(null);

  // Permission checks
  const canReadUsers = hasPermission('read users');
  const canCreateUsers = hasPermission('create users');
  const canUpdateUsers = hasPermission('update users');
  const canDeleteUsers = hasPermission('delete users');
  const [addFormData, setAddFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    roleId: '',
  });
  const [editFormData, setEditFormData] = useState({
    name: '',
    email: '',
    roleId: '',
  });

  const fetchUsers = useCallback(async (page: number) => {
    try {
      setIsLoading(true);
      console.log(`Fetching users for page ${page} with pageSize ${PAGE_SIZE}`);
      const response = await fetch(`${API_BASE_URL}/users?page=${page}&pageSize=${PAGE_SIZE}`, {
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
        },
      });
      console.log('Fetch users status:', response.status, response.statusText);
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Fetch users error response:', errorText);
        throw new Error(
          response.status === 401
            ? 'Unauthorized: Please log in'
            : response.status === 403
            ? 'Forbidden: You do not have permission to read users'
            : `Failed to fetch users: ${response.status} ${errorText}`
        );
      }
      const data = await response.json();
      console.log('Fetched users data:', JSON.stringify(data, null, 2));
      const usersData = data.data?.users || [];
      console.log('Setting users:', JSON.stringify(usersData, null, 2));
      setUsers(usersData);
      setTotalPages(data.data?.totalPages || 1);
      setTotalItems(data.data?.total || 0);
      setCurrentPage(page);
    } catch (error: any) {
      console.error('Fetch users error:', error.message, error.stack);
      toast.error(error.message || 'Failed to fetch users');
      setUsers([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchRoles = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/roles`, {
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
        },
      });
      console.log('Fetch roles status:', response.status, response.statusText);
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Fetch roles error response:', errorText);
        throw new Error(
          response.status === 401
            ? 'Unauthorized: Please log in'
            : response.status === 403
            ? 'Forbidden: You do not have permission to read roles'
            : `Failed to fetch roles: ${response.status} ${errorText}`
        );
      }
      const data = await response.json();
      console.log('Fetched roles data:', JSON.stringify(data, null, 2));
      setRoles(data.data?.roles || []);
    } catch (error: any) {
      console.error('Fetch roles error:', error.message, error.stack);
      toast.error(error.message || 'Failed to fetch roles');
    }
  }, []);

  const fetchUserForEdit = useCallback(async (id: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/users/${id}`, {
        credentials: 'include',
        headers: { 'Accept': 'application/json' },
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch user: ${response.status} ${errorText}`);
      }
      const data = await response.json();
      console.log('Fetched user data:', JSON.stringify(data, null, 2));
      setEditFormData({
        name: data.data.name || '',
        email: data.data.email || '',
        roleId: data.data.roleId || '',
      });
    } catch (error: any) {
      console.error('Fetch user error:', error);
      toast.error(error.message || 'Failed to fetch user');
    }
  }, []);

  useEffect(() => {
    fetchUsers(currentPage);
    fetchRoles();
  }, [fetchUsers, fetchRoles, currentPage]);

  // Early returns moved after all hooks
  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <span>Loading permissions...</span>
      </div>
    );
  }

  if (!canReadUsers) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You don&apos;t have permission to view users.</p>
        </div>
      </div>
    );
  }

  const handleDeleteClick = (id: string) => {
    setDeletingUserId(id);
    setIsConfirmDialogOpen(true);
  };

  const handleEditClick = (id: string) => {
    setEditUserId(id);
    fetchUserForEdit(id);
    setIsEditOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingUserId) return;
    try {
      const response = await fetch(`${API_BASE_URL}/users/${deletingUserId}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
        },
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Delete user response:', response.status, errorText);
        throw new Error(
          response.status === 401
            ? 'Unauthorized: Please log in'
            : response.status === 403
            ? 'Forbidden: You do not have permission to delete users'
            : `Failed to delete user: ${response.status} ${errorText}`
        );
      }
      setUsers(users.filter((user) => user.id !== deletingUserId));
      toast.success('User deleted successfully');
    } catch (error: any) {
      console.error('Delete user error:', error);
      toast.error(error.message || 'Failed to delete user');
    } finally {
      setDeletingUserId(null);
      setIsConfirmDialogOpen(false);
    }
  };

  const handleAddInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAddFormData({ ...addFormData, [e.target.name]: e.target.value });
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditFormData({ ...editFormData, [e.target.name]: e.target.value });
  };

  const handleAddRoleChange = (value: string) => {
    setAddFormData({ ...addFormData, roleId: value });
  };

  const handleEditRoleChange = (value: string) => {
    setEditFormData({ ...editFormData, roleId: value });
  };

  const handleAddSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (addFormData.password !== addFormData.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }
    if (!addFormData.roleId) {
      toast.error('Please select a role');
      return;
    }
    try {
      const response = await fetch(`${API_BASE_URL}/users`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
        body: JSON.stringify({
          name: `${addFormData.firstName} ${addFormData.lastName}`.trim(),
          email: addFormData.email,
          password: addFormData.password,
          roleId: addFormData.roleId,
        }),
        credentials: 'include',
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Create user response:', response.status, errorText);
        throw new Error(
          response.status === 401
            ? 'Unauthorized: Please log in'
            : response.status === 403
            ? 'Forbidden: You do not have permission to create users'
            : response.status === 400
            ? errorText
            : `Failed to create user: ${response.status} ${errorText}`
        );
      }
      const data = await response.json();
      console.log('Create user response data:', JSON.stringify(data, null, 2));
      toast.success('User created successfully');
      setIsAddOpen(false);
      setAddFormData({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        confirmPassword: '',
        roleId: '',
      });
      fetchUsers(currentPage);
    } catch (error: any) {
      console.error('Create user error:', error);
      toast.error(error.message || 'Failed to create user');
    }
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editUserId) return;
    try {
      const response = await fetch(`${API_BASE_URL}/users/${editUserId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
        body: JSON.stringify({
          name: editFormData.name,
          email: editFormData.email,
          roleId: editFormData.roleId,
        }),
        credentials: 'include',
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Update user response:', response.status, errorText);
        throw new Error(
          response.status === 401
            ? 'Unauthorized: Please log in'
            : response.status === 403
            ? 'Forbidden: You do not have permission to update users'
            : `Failed to update user: ${response.status} ${errorText}`
        );
      }
      toast.success('User updated successfully');
      setIsEditOpen(false);
      setEditUserId(null);
      setEditFormData({ name: '', email: '', roleId: '' });
      fetchUsers(currentPage);
    } catch (error: any) {
      console.error('Update user error:', error);
      toast.error(error.message || 'Failed to update user');
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };

  const columns: ColumnDef<User>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => <span className="font-medium">{row.original.name || 'N/A'}</span>,
    },
    {
      accessorKey: 'email',
      header: 'Email',
    },
    {
      accessorKey: 'role',
      header: 'Role',
    },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      cell: ({ row }) => formatDate(row.original.createdAt),
    },
    {
      id: 'actions',
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => (
        <div className="flex justify-end gap-2">
          {canUpdateUsers && (
            <Button
              variant="outline"
              size="icon"
              onClick={() => handleEditClick(row.original.id)}
            >
              <Pencil className="h-4 w-4" />
            </Button>
          )}
          {canDeleteUsers && (
            <Button
              variant="destructive"
              size="icon"
              onClick={() => handleDeleteClick(row.original.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Users className="h-6 w-6" />
            User Management
          </h1>
          <div className="flex gap-4">
            {canCreateUsers && (
              <Dialog open={isAddOpen} onOpenChange={setIsAddOpen}>
                <DialogTrigger asChild>
                  <Button className="text-base h-10">
                    <Users className="h-5 w-5 mr-2" />
                    Add User
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-[90vw] sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle className="text-2xl">Add New User</DialogTitle>
                    <div className="border-b border-gray-200 w-full mb-4"></div>
                  </DialogHeader>
                  <form className="space-y-4" onSubmit={handleAddSubmit}>
                    <div className="grid gap-2">
                      <Label htmlFor="firstName" className="text-base">First Name</Label>
                      <Input
                        id="firstName"
                        name="firstName"
                        placeholder="Enter first name"
                        value={addFormData.firstName}
                        onChange={handleAddInputChange}
                        required
                        className="text-base p-2 h-10"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="lastName" className="text-base">Last Name</Label>
                      <Input
                        id="lastName"
                        name="lastName"
                        placeholder="Enter last name"
                        value={addFormData.lastName}
                        onChange={handleAddInputChange}
                        required
                        className="text-base p-2 h-10"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="email" className="text-base">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="Enter email"
                        value={addFormData.email}
                        onChange={handleAddInputChange}
                        required
                        className="text-base p-2 h-10"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="password" className="text-base">Password</Label>
                      <Input
                        id="password"
                        name="password"
                        type="password"
                        placeholder="Enter password"
                        value={addFormData.password}
                        onChange={handleAddInputChange}
                        required
                        className="text-base p-2 h-10"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="confirmPassword" className="text-base">Confirm Password</Label>
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type="password"
                        placeholder="Confirm password"
                        value={addFormData.confirmPassword}
                        onChange={handleAddInputChange}
                        required
                        className="text-base p-2 h-10"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="role" className="text-base">Role</Label>
                      <Select name="role" onValueChange={handleAddRoleChange} value={addFormData.roleId}>
                        <SelectTrigger id="role" className="text-base p-2 h-10">
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                        <SelectContent>
                          {roles.length === 0 ? (
                            <div className="text-sm text-gray-500 p-2">No roles available. Create a role first.</div>
                          ) : (
                            roles.map((role) => (
                              <SelectItem key={role.id} value={role.id} className="text-base">
                                {role.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex justify-end gap-4 pt-4 sticky bottom-0 bg-white">
                      <Button variant="outline" onClick={() => setIsAddOpen(false)} className="text-base h-10">
                        Cancel
                      </Button>
                      <Button type="submit" className="text-base h-10">
                        Add User
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            )}
            <Button variant="outline" className="text-base h-10">
              <Download className="h-5 w-5 mr-2" />
              Download xlsx
            </Button>
          </div>
        </div>
        <DataTable
          columns={columns}
          data={users}
          totalItems={totalItems}
          totalPages={totalPages}
          currentPage={currentPage}
          pageSize={PAGE_SIZE}
          onPageChange={(page) => {
            if (page !== currentPage) {
              setCurrentPage(page);
            }
          }}
          isLoading={isLoading}
        />
        <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
          <DialogContent className="max-w-[90vw] sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-2xl">Edit User</DialogTitle>
              <div className="border-b border-gray-200 w-full mb-4"></div>
            </DialogHeader>
            <form className="space-y-4" onSubmit={handleEditSubmit}>
              <div className="grid gap-2">
                <Label htmlFor="name" className="text-base">Name</Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Enter name"
                  value={editFormData.name}
                  onChange={handleEditInputChange}
                  required
                  className="text-base p-2 h-10"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email" className="text-base">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter email"
                  value={editFormData.email}
                  onChange={handleEditInputChange}
                  required
                  className="text-base p-2 h-10"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="role" className="text-base">Role</Label>
                <Select name="role" onValueChange={handleEditRoleChange} value={editFormData.roleId}>
                  <SelectTrigger id="role" className="text-base p-2 h-10">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.length === 0 ? (
                      <div className="text-sm text-gray-500 p-2">No roles available.</div>
                    ) : (
                      roles.map((role) => (
                        <SelectItem key={role.id} value={role.id} className="text-base">
                          {role.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-4 pt-4 sticky bottom-0 bg-white">
                <Button variant="outline" onClick={() => setIsEditOpen(false)} className="text-base h-10">
                  Cancel
                </Button>
                <Button type="submit" className="text-base h-10">
                  Update User
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
        <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the user.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteConfirm}>Delete</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default AddUser;