'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/app-components/dataTable';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Users, Trash2, Pencil } from 'lucide-react';

interface Role {
  id: string;
  name: string;
  permissions: { permissionName: string }[];
}

interface PermissionModule {
  [key: string]: string;
}

interface Permissions {
  [module: string]: PermissionModule;
}

const API_BASE_URL = 'http://localhost:4005/api/v1';
const PAGE_SIZE = 10;

const RolePage = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permissions | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPermissionsLoading, setIsPermissionsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingRoleId, setEditingRoleId] = useState<string | null>(null);
  const [deletingRoleId, setDeletingRoleId] = useState<string | null>(null);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [roleName, setRoleName] = useState('');
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  const fetchPermissions = useCallback(async () => {
    try {
      setIsPermissionsLoading(true);
      const response = await fetch(`${API_BASE_URL}/constants/db-permissions`, {
        credentials: 'include',
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          response.status === 403
            ? 'Forbidden: You do not have permission to read roles'
            : response.status === 401
            ? 'Unauthorized: Please log in'
            : response.status === 404
            ? 'No permissions found in database'
            : `Failed to fetch permissions: ${errorText}`
        );
      }
      const data = await response.json();
      if (!data.data.permissions || Object.keys(data.data.permissions).length === 0) {
        throw new Error('No permissions returned from server');
      }
      setPermissions(data.data.permissions);
    } catch (error: any) {
      console.error('Fetch permissions error:', error);
      toast.error(error.message || 'Failed to fetch permissions');
    } finally {
      setIsPermissionsLoading(false);
    }
  }, []);

  const fetchRoles = useCallback(async (page: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/roles?page=${page}&pageSize=${PAGE_SIZE}`, {
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error(
          response.status === 403
            ? 'Forbidden: You do not have permission to view roles'
            : response.status === 401
            ? 'Unauthorized: Please log in'
            : 'Failed to fetch roles'
        );
      }
      const data = await response.json();
      setRoles(data.data.roles || []);
      setTotalPages(data.data.totalPages || 1);
      setTotalItems(data.data.total || 0);
      setCurrentPage(page);
    } catch (error: any) {
      console.error('Fetch roles error:', error);
      toast.error(error.message || 'Failed to fetch roles');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchRoleById = async (id: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/roles/${id}`, {
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error(
          response.status === 403
            ? 'Forbidden: You do not have permission to view this role'
            : response.status === 401
            ? 'Unauthorized: Please log in'
            : 'Failed to fetch role'
        );
      }
      const data = await response.json();
      setRoleName(data.data.name);
      setSelectedPermissions(data.data.permissions.map((p: { permissionName: string }) => p.permissionName));
      setEditingRoleId(id);
      setIsEditMode(true);
      setIsOpen(true);
    } catch (error: any) {
      console.error('Fetch role error:', error);
      toast.error(error.message || 'Failed to fetch role');
    }
  };

  useEffect(() => {
    fetchPermissions();
    fetchRoles(currentPage);
  }, [fetchPermissions, fetchRoles, currentPage]);

  const handleDeleteClick = (id: string) => {
    setDeletingRoleId(id);
    setIsConfirmDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingRoleId) return;
    try {
      const response = await fetch(`${API_BASE_URL}/roles/${deletingRoleId}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error(
          response.status === 403
            ? 'Forbidden: You do not have permission to delete this role'
            : response.status === 401
            ? 'Unauthorized: Please log in'
            : 'Failed to delete role'
        );
      }
      setRoles(roles.filter((role) => role.id !== deletingRoleId));
      toast.success('Role deleted successfully');
    } catch (error: any) {
      console.error('Delete role error:', error);
      toast.error(error.message || 'Failed to delete role');
    } finally {
      setDeletingRoleId(null);
      setIsConfirmDialogOpen(false);
    }
  };

  const handleSaveRole = async () => {
    if (!roleName) {
      toast.error('Role name is required');
      return;
    }
    try {
      const url = isEditMode ? `${API_BASE_URL}/roles/${editingRoleId}` : `${API_BASE_URL}/roles`;
      const method = isEditMode ? 'PUT' : 'POST';
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: roleName, permissions: selectedPermissions }),
        credentials: 'include',
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          response.status === 403
            ? `Forbidden: You do not have permission to ${isEditMode ? 'update' : 'create'} roles`
            : response.status === 401
            ? 'Unauthorized: Please log in'
            : response.status === 400
            ? errorText
            : `Failed to ${isEditMode ? 'update' : 'create'} role`
        );
      }
      toast.success(`Role ${isEditMode ? 'updated' : 'created'} successfully`);
      setIsOpen(false);
      setRoleName('');
      setSelectedPermissions([]);
      setIsEditMode(false);
      setEditingRoleId(null);
      fetchRoles(currentPage);
    } catch (error: any) {
      console.error('Save role error:', error);
      toast.error(error.message || `Failed to ${isEditMode ? 'update' : 'create'} role`);
    }
  };

  const handleSelectAll = (module: string) => {
    if (!permissions) return;
    const modulePermissions = Object.keys(permissions[module]);
    setSelectedPermissions((prev) =>
      prev.includes(modulePermissions[0])
        ? prev.filter((p) => !modulePermissions.includes(p))
        : [...new Set([...prev, ...modulePermissions])]
    );
  };

  const handleGlobalSelectAll = () => {
    if (!permissions) return;
    const allPermissions = Object.values(permissions).flatMap((module) => Object.keys(module));
    setSelectedPermissions((prev) =>
      prev.length === allPermissions.length ? [] : [...new Set(allPermissions)]
    );
  };

  const columns: ColumnDef<Role>[] = [
    {
      accessorKey: 'name',
      header: 'Role Name',
      cell: ({ row }) => <span className="font-medium">{row.original.name}</span>,
    },
    {
      id: 'actions',
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => (
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => fetchRoleById(row.original.id)}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="destructive"
            size="icon"
            onClick={() => handleDeleteClick(row.original.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (isPermissionsLoading) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!permissions) {
    return (
      <div className="container mx-auto py-6 px-4">
        <h1 className="text-2xl font-bold flex items-center gap-2">
          <Users className="h-6 w-6" />
          Roles Management
        </h1>
        <div className="mt-4 flex items-center gap-4">
          <p className="text-red-600">Failed to load permissions. Please try again later.</p>
          <Button variant="outline" onClick={fetchPermissions}>
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Users className="h-6 w-6" />
            Roles Management
          </h1>
          <Dialog
            open={isOpen}
            onOpenChange={(open) => {
              setIsOpen(open);
              if (!open) {
                setIsEditMode(false);
                setEditingRoleId(null);
                setRoleName('');
                setSelectedPermissions([]);
              }
            }}
          >
            <DialogTrigger asChild>
              <Button className="text-base h-10">
                <Users className="h-5 w-5 mr-2" />
                Add Role
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-[90vw] sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="text-2xl">
                  {isEditMode ? 'Edit Role' : 'Add New Role'}
                </DialogTitle>
                <div className="border-b border-gray-200 w-full mb-4"></div>
              </DialogHeader>
              <div className="space-y-6">
                <div className="grid gap-2">
                  <Label htmlFor="name" className="text-base">
                    Role Name
                  </Label>
                  <Input
                    id="name"
                    placeholder="Enter role name"
                    value={roleName}
                    onChange={(e) => setRoleName(e.target.value)}
                    required
                    className="text-base p-2 h-10"
                  />
                </div>
                <div>
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Module Permissions</h3>
                    <Button variant="outline" onClick={handleGlobalSelectAll}>
                      Select All Permissions
                    </Button>
                  </div>
                  <div className="space-y-4">
                    {Object.entries(permissions).map(([module, perms]) => (
                      <div key={module} className="border rounded-lg p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="text-md font-medium">
                            {module.replace('_', ' ').toUpperCase()}
                          </h4>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSelectAll(module)}
                          >
                            Select All
                          </Button>
                        </div>
                        <div className="space-y-2">
                          {Object.entries(perms).map(([perm, label]) => (
                            <div key={perm} className="flex items-center">
                              <input
                                type="checkbox"
                                id={perm}
                                value={perm}
                                checked={selectedPermissions.includes(perm)}
                                onChange={(e) => {
                                  setSelectedPermissions((prev) =>
                                    e.target.checked
                                      ? [...new Set([...prev, perm])]
                                      : prev.filter((p) => p !== perm)
                                  );
                                }}
                                className="mr-2 h-4 w-4"
                              />
                              <label htmlFor={perm} className="text-sm">
                                {label}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex justify-end gap-4 pt-4 sticky bottom-0 bg-white">
                  <Button
                    variant="outline"
                    onClick={() => setIsOpen(false)}
                    className="text-base h-10"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSaveRole}
                    className="text-base h-10"
                  >
                    {isEditMode ? 'Update Role' : 'Save Role'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
        <DataTable
          columns={columns}
          data={roles}
          totalItems={totalItems}
          totalPages={totalPages}
          currentPage={currentPage}
          pageSize={PAGE_SIZE}
          onPageChange={(page) => {
            if (page !== currentPage) {
              setCurrentPage(page);
            }
          }}
          isLoading={isLoading}
        />
        <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the role.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteConfirm}>Delete</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default RolePage;