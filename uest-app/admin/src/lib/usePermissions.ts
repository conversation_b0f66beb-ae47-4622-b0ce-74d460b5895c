'use client';

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

interface User {
  id: string;
  name: string | null;
  email: string;
  role: {
    id: string;
    name: string;
    permissions: string[];
  } | null;
}

export function usePermissions() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchUserPermissions = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log('Fetching user permissions from /api/v1/auth-admin/me');
      const response = await fetch('http://localhost:4005/api/v1/auth-admin/me', {
        credentials: 'include',
        headers: { 'Accept': 'application/json' },
      });
      console.log('Response status:', response.status, 'Headers:', response.headers.get('content-type'));
      const data = await response.json();
      console.log('Received data:', JSON.stringify(data, null, 2));
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}: ${data.message || 'Failed to fetch user permissions'}`);
      }
      if (!data.success || !data.data) {
        throw new Error('Invalid response format from /auth-admin/me');
      }
      setUser(data.data);
    } catch (error: any) {
      console.error('Fetch permissions error:', {
        message: error.message,
        stack: error.stack,
        response: error.response,
      });
      toast.error(error.message || 'Failed to fetch permissions');
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUserPermissions();
  }, [fetchUserPermissions]);

  const hasPermission = useCallback((permission: string): boolean => {
    const result = !!user?.role?.permissions?.includes(permission) || user?.email === '<EMAIL>';
    console.log(`Checking permission "${permission}": ${result}`);
    return result;
  }, [user]);

  const hasAnyPermission = useCallback((permissions: string[]): boolean => {
    const result = permissions.some((perm) => user?.role?.permissions?.includes(perm)) || user?.email === '<EMAIL>';
    console.log(`Checking any permissions [${permissions.join(', ')}]: ${result}`);
    return result;
  }, [user]);

  return { user, isLoading, hasPermission, hasAnyPermission, refreshPermissions: fetchUserPermissions };
}