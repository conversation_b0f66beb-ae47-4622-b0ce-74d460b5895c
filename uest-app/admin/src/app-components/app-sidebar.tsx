'use client';

import * as React from 'react';
import {
  ActivitySquareIcon,
  ClipboardListIcon,
  LayoutDashboardIcon,
  UserCheck,
  Brain,
  Star,
  MessageSquareText,
  Notebook,
  Share2,
  Settings,
  FolderTree,
  PenSquare,
  Camera,
  MessageSquareTextIcon,
  Bell,
  Users,
  UserPlus,
  Shield,
  ShoppingBagIcon,
  ShoppingCartIcon,
} from 'lucide-react';
import { NavMain } from '@/app-components/nav-main';
import { NavUser } from '@/app-components/nav-user';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import Image from 'next/image';
import { usePermissions } from '@/lib/usePermissions';
import { filterMenuByPermissions } from '@/lib/menuUtils';

const data = {
  user: {
    name: 'uest admin',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
  navMain: [
    {
      title: 'Classes',
      icon: LayoutDashboardIcon,
      permissions: ['read blog', 'read testimonials', 'read thoughts','read classes'],
      children: [
        { title: 'Dashboard', url: '/dashboard', icon: LayoutDashboardIcon ,permission: 'read classes' },
        { title: 'Testimonials', url: '/testimonials', icon: MessageSquareText, permission: 'read testimonials' },
        { title: 'Thoughts', url: '/classes-thoughts', icon: Brain, permission: 'read thoughts' },
        { title: 'Blogs', url: '/blog', icon: Notebook, permission: 'read blog' },
      ],
    },
    {
      title: 'Students',
      icon: UserCheck,
      permissions: ['read student details', 'read reviews'],
      children: [
        { title: 'Student Details', url: '/student-details', icon: UserCheck, permission: 'read student details' },
        { title: 'Reviews', url: '/reviews', icon: Star, permission: 'read reviews' },
      ],
    },
    {
      title: 'Exam',
      icon: ClipboardListIcon,
      permissions: ['read exam details', 'read question bank', 'read photo monitoring'],
      children: [
        { title: 'Exam Details', url: '/exam-detail', icon: ClipboardListIcon, permission: 'read exam details' },
        { title: 'Question Bank', url: '/question-bank', icon: ActivitySquareIcon, permission: 'read question bank' },
        { title: 'Photo Monitoring', url: '/exam-monitoring', icon: Camera, permission: 'read photo monitoring' },,
        {title: 'Mock Exam Question Bank', url: '/mock-question-bank', icon: PenSquare},
      ],
    },
   {
      title: 'Referrals',
      icon: Share2,
      permissions: ['read referrals'],
      children: [
          { title: 'Referral Management', url: '/referral-management', icon: Share2, permission: 'read referrals' },
      ],
    },
    {
      title: 'Constants',
      icon: Settings,
      children: [
        { title: 'Manage Categories', url: '/constants', icon: FolderTree },
      ],
    },
    {
      title: 'Chats',
      icon: MessageSquareTextIcon,
      permissions: ['read chats'],
      children: [
        { title: 'Chats', url: '/chat', icon: MessageSquareTextIcon, permission: 'read chats' },
      ],
    },
    {
      title: 'User Setup',
      icon: Users,
      permissions: ['read users', 'read role'],
      children: [
        { title: 'Manage User', url: '/user-setup/add-user', icon: UserPlus, permission: 'read users'},
        { title: 'Roles', url: '/user-setup/roles', icon: Shield, permission: 'read role'},
      ],
    },
    {
      title: 'Notifications',
      icon: Bell,
      children: [
        { title: 'All Notifications', url: '/notifications', icon: Bell },
      ]
    },
    {
      title: 'Store',
      icon: ShoppingBagIcon,
      children: [
        { title: 'Store Items', url: '/store', icon: ShoppingBagIcon },
        { title: 'Orders', url: '/store-orders', icon: ShoppingCartIcon },
      ]
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, hasPermission, hasAnyPermission, isLoading } = usePermissions();

  if (isLoading) {
    return (
      <Sidebar collapsible="offcanvas" {...props}>
        <SidebarHeader>
          <Image src="/logo.jpeg" alt="App Logo" width={130} height={40} className="rounded-md" />
        </SidebarHeader>
        <SidebarContent>
          <div className="flex justify-center items-center h-64">
            <span>Loading permissions...</span>
          </div>
        </SidebarContent>
      </Sidebar>
    );
  }

  const filteredNavMain = filterMenuByPermissions(data.navMain, hasPermission, hasAnyPermission);

  // Create dynamic user object for NavUser component
  const dynamicUser = {
    name: user?.name || 'Unknown User',
    email: user?.email || '<EMAIL>',
    avatar: '/avatars/shadcn.jpg', // Keep default avatar
  };

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <a href="#">
                <Image
                  src="/logo.jpeg"
                  alt="App Logo"
                  width={130}
                  height={40}
                  className="rounded-md"
                />
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={filteredNavMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={dynamicUser} />
      </SidebarFooter>
    </Sidebar>
  );
}