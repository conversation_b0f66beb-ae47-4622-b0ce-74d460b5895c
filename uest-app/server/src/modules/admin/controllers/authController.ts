import { Request, Response } from 'express';
import { generateToken } from '@/utils/jwt';
import { comparePassword, findUserByEmail, findUserWithRoleById } from '../services/authService';
import { sendSuccess, sendError } from '@/utils/response';

export async function login(req: Request, res: Response): Promise<void> {
  try {
    const { email, password } = req.body;
    const user = await findUserByEmail(email);

    if (!user) {
      sendError(res, 'Invalid credentials', 401);
      return;
    }

    const isMatch = await comparePassword(password, user.password);
    if (!isMatch) {
      sendError(res, 'Invalid credentials', 401);
      return;
    }

    const token = generateToken({ id: user.id, email: user.email });

    res.cookie('admin_jwt', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000,
    });

    sendSuccess(res, { id: user.id, name: user.name, email: user.email }, 'Login successful');
  } catch (error: any) {
    console.error('Login Error:', error);
    sendError(res, 'Login failed', 500);
  }
}

export async function logout(req: Request, res: Response): Promise<void> {
  res.clearCookie('admin_jwt', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
  });

  sendSuccess(res, null, 'Logged out successfully');
}

export async function me(req: Request, res: Response): Promise<void> {
  try {
    if (!req.user?.id) {
      sendError(res, 'Unauthorized', 401);
      return;
    }

    const user = await findUserWithRoleById(req.user.id);

    if (!user) {
      sendError(res, 'User not found', 404);
      return;
    }

    const permissions =
      user.role?.permissions?.map((p) => p.permission?.permissionName).filter(Boolean) || [];

    sendSuccess(res, {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role
        ? {
            id: user.role.id,
            name: user.role.name,
            permissions,
          }
        : null,
    });
  } catch (error: any) {
    console.error('Fetch /me error:', error);
    sendError(res, 'Something went wrong!', 500);
  }
}
