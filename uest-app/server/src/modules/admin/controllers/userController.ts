import { Request, Response } from 'express';
import prisma from '@/config/prismaClient';
import { sendError, sendSuccess } from '@/utils/response';
import bcrypt from 'bcryptjs';

export const getUsers = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const pageSize = parseInt(req.query.pageSize as string) || 10;
    const skip = (page - 1) * pageSize;

    const [users, total] = await Promise.all([
      prisma.adminUser.findMany({
        skip,
        take: pageSize,
        include: { role: true },
      }),
      prisma.adminUser.count(),
    ]);

    sendSuccess(res, {
      users: users.map((user) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role ? { id: user.role.id, name: user.role.name } : null,
        createdAt: user.createdAt,
      })),
      total,
      totalPages: Math.ceil(total / pageSize),
      currentPage: page,
    });
  } catch (error: any) {
    console.error('Fetch users error:', error);
    sendError(res, 'Failed to fetch users', 500);
  }
};

export const getUserById = async (req: Request, res: Response) => {
  try {
    const user = await prisma.adminUser.findUnique({
      where: { id: req.params.id },
      include: { role: true },
    });
    if (!user) {
      sendError(res, 'User not found', 404);
      return;
    }
    sendSuccess(res, {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role ? { id: user.role.id, name: user.role.name } : null,
      createdAt: user.createdAt,
    });
  } catch (error: any) {
    console.error('Fetch user error:', error);
    sendError(res, 'Failed to fetch user', 500);
  }
};

export const createUser = async (req: Request, res: Response) => {
  try {
    const { name, email, password, roleId } = req.body;
    if (!email || !password) {
      sendError(res, 'Email and password are required', 400);
      return;
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const user = await prisma.adminUser.create({
      data: {
        id: crypto.randomUUID(),
        name,
        email,
        password: hashedPassword,
        roleId,
        createdAt: new Date(),
      },
      include: { role: true },
    });

    sendSuccess(res, {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role ? { id: user.role.id, name: user.role.name } : null,
      createdAt: user.createdAt,
    });
  } catch (error: any) {
    console.error('Create user error:', error);
    if (error.code === 'P2002') {
      sendError(res, `Email '${req.body.email}' already exists`, 400);
    } else {
      sendError(res, 'Failed to create user', 500);
    }
  }
};

export const updateUser = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, email, password, roleId } = req.body;
    const data: any = { name, email };
    if (password) {
      data.password = await bcrypt.hash(password, 10);
    }
    if (roleId) {
      data.roleId = roleId;
    }

    const user = await prisma.adminUser.update({
      where: { id },
      data,
      include: { role: true },
    });

    sendSuccess(res, {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role ? { id: user.role.id, name: user.role.name } : null,
      createdAt: user.createdAt,
    });
  } catch (error: any) {
    console.error('Update user error:', error);
    if (error.code === 'P2002') {
      sendError(res, `Email '${req.body.email}' already exists`, 400);
    } else if (error.code === 'P2025') {
      sendError(res, 'User not found', 404);
    } else {
      sendError(res, 'Failed to update user', 500);
    }
  }
};

export const deleteUser = async (req: Request, res: Response) => {
  try {
    await prisma.adminUser.delete({ where: { id: req.params.id } });
    sendSuccess(res, { message: 'User deleted' });
  } catch (error: any) {
    console.error('Delete user error:', error);
    if (error.code === 'P2025') {
      sendError(res, 'User not found', 404);
    } else {
      sendError(res, 'Failed to delete user', 500);
    }
  }
};

export const disconnect = async () => {
  await prisma.$disconnect();
};