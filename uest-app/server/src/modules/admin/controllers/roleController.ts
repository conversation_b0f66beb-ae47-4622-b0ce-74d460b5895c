import { Request, Response } from 'express';
import { sendSuccess, sendError } from '@/utils/response';
import { roleService } from '@/modules/admin/services/roleService';

export const roleController = {
  getAllRoles: async (req: Request, res: Response) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const pageSize = parseInt(req.query.pageSize as string) || 10;
      const result = await roleService.getAllRoles(page, pageSize);
      sendSuccess(res, result);
    } catch (error: any) {
      sendError(res, error.message || 'Failed to fetch roles', 500);
    }
  },

  getRoleById: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const role = await roleService.getRoleById(id);
      sendSuccess(res, role);
    } catch (error: any) {
      sendError(res, error.message || 'Failed to fetch role', error.message === 'Role not found' ? 404 : 500);
    }
  },

  createRole: async (req: Request, res: Response) => {
    try {
      const { name, permissions } = req.body;
      if (!name || !permissions) {
        return sendError(res, 'Role name and permissions are required', 400);
      }
      const role = await roleService.createRole(name, permissions);
      sendSuccess(res, role);
    } catch (error: any) {
      sendError(res, error.message || 'Failed to create role', error.message.includes('already exists') ? 400 : 500);
    }
  },

  updateRole: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { name, permissions } = req.body;
      if (!name || !permissions) {
        return sendError(res, 'Role name and permissions are required', 400);
      }
      const role = await roleService.updateRole(id, name, permissions);
      sendSuccess(res, role);
    } catch (error: any) {
      sendError(res, error.message || 'Failed to update role', error.message.includes('already exists') || error.message === 'Role not found' ? 400 : 500);
    }
  },

  deleteRole: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const result = await roleService.deleteRole(id);
      sendSuccess(res, result);
    } catch (error: any) {
      sendError(res, error.message || 'Failed to delete role', error.message === 'Role not found' ? 404 : 500);
    }
  },
};