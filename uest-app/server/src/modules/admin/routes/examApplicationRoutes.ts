import express from 'express';
import { applyForExam, getAllExamApplications } from '../controllers/examApplicationController';
import { authMiddleware } from '@/middlewares/adminAuth';
import { permissionMiddleware } from '@/middlewares/adminAuth';

const examApplicationRouter = express.Router();

examApplicationRouter.post('/apply', authMiddleware, permissionMiddleware('read exam applications'), applyForExam);
examApplicationRouter.get('/', authMiddleware, permissionMiddleware('read exam applications'), getAllExamApplications);

export default examApplicationRouter;
