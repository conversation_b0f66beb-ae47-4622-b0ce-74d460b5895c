import express, { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { permissionMiddleware } from '@/middlewares/adminAuth';
import { sendError, sendSuccess } from '@/utils/response';

const router = express.Router();
const prisma = new PrismaClient();

router.get('/', permissionMiddleware('read role'), async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const pageSize = parseInt(req.query.pageSize as string) || 10;
    const skip = (page - 1) * pageSize;

    const [roles, total] = await Promise.all([
      prisma.role.findMany({
        skip,
        take: pageSize,
        include: { permissions: { include: { permission: true } } },
      }),
      prisma.role.count(),
    ]);

    sendSuccess(res, {
      roles: roles.map((role) => ({
        id: role.id,
        name: role.name,
        permissions: role.permissions.map((p) => p.permission),
      })),
      total,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch{
    sendError(res, 'Failed to fetch roles', 500);
  } finally {
    await prisma.$disconnect();
  }
});

router.get('/:id', permissionMiddleware('read role'), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const role = await prisma.role.findUnique({
      where: { id },
      include: { permissions: { include: { permission: true } } },
    });
    if (!role) {
      sendError(res, 'Role not found', 404);
      return;
    }
    sendSuccess(res, {
      id: role.id,
      name: role.name,
      permissions: role.permissions.map((p) => p.permission),
    });
  } catch  {
    sendError(res, 'Failed to fetch role', 500);
  } finally {
    await prisma.$disconnect();
  }
});

router.post('/', permissionMiddleware('create role'), async (req: Request, res: Response) => {
  try {
    const { name, permissions } = req.body;
    if (!name || !permissions || !Array.isArray(permissions)) {
      sendError(res, 'Role name and permissions array are required', 400);
      return;
    }

    const existingPermissions = await prisma.permission.findMany({
      where: { permissionName: { in: permissions } },
      select: { permissionName: true },
    });
    const validPermissions = existingPermissions.map((p) => p.permissionName);
    const invalidPermissions = permissions.filter((p: string) => !validPermissions.includes(p));
    if (invalidPermissions.length > 0) {
      sendError(res, `Invalid permissions: ${invalidPermissions.join(', ')}`, 400);
      return;
    }

    const role = await prisma.role.create({
      data: {
        id: crypto.randomUUID(),
        name,
        createdAt: new Date(),
        permissions: {
          create: permissions.map((perm: string) => ({
            id: crypto.randomUUID(),
            permission: { connect: { permissionName: perm } },
          })),
        },
      },
      include: { permissions: { include: { permission: true } } },
    });
    sendSuccess(res, {
      id: role.id,
      name: role.name,
      permissions: role.permissions.map((p) => p.permission),
    });
  } catch (error: any) {
    if (error.code === 'P2002') {
      sendError(res, `Role name '${req.body.name}' already exists`, 400);
    } else {
      sendError(res, 'Failed to create role', 500);
    }
  } finally {
    await prisma.$disconnect();
  }
});

router.put('/:id', permissionMiddleware('update role'), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, permissions } = req.body;
    if (!name || !permissions || !Array.isArray(permissions)) {
      sendError(res, 'Role name and permissions array are required', 400);
      return;
    }

    const existingPermissions = await prisma.permission.findMany({
      where: { permissionName: { in: permissions } },
      select: { permissionName: true },
    });
    const validPermissions = existingPermissions.map((p) => p.permissionName);
    const invalidPermissions = permissions.filter((p: string) => !validPermissions.includes(p));
    if (invalidPermissions.length > 0) {
      sendError(res, `Invalid permissions: ${invalidPermissions.join(', ')}`, 400);
      return;
    }

    await prisma.roleHasPermission.deleteMany({ where: { roleId: id } });

    const role = await prisma.role.update({
      where: { id },
      data: {
        name,
        permissions: {
          create: permissions.map((perm: string) => ({
            id: crypto.randomUUID(),
            permission: { connect: { permissionName: perm } },
          })),
        },
      },
      include: { permissions: { include: { permission: true } } },
    });
    sendSuccess(res, {
      id: role.id,
      name: role.name,
      permissions: role.permissions.map((p) => p.permission),
    });
  } catch (error: any) {
    if (error.code === 'P2002') {
      sendError(res, `Role name '${req.body.name}' already exists`, 400);
    } else if (error.code === 'P2025') {
      sendError(res, 'Role not found', 404);
    } else {
      sendError(res, 'Failed to update role', 500);
    }
  } finally {
    await prisma.$disconnect();
  }
});

router.delete('/:id', permissionMiddleware('delete role'), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await prisma.role.delete({ where: { id } });
    sendSuccess(res, { message: 'Role deleted' });
  } catch (error: any) {
    if (error.code === 'P2025') {
      sendError(res, 'Role not found', 404);
    } else {
      sendError(res, 'Failed to delete role', 500);
    }
  } finally {
    await prisma.$disconnect();
  }
});

export default router;