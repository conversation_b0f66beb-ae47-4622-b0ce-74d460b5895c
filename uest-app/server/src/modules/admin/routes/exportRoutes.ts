import express from 'express';
import {
  exportClassesToExcel,
  exportExamApplicationsToExcel,
  exportStudentsToExcel,
} from '../controllers/exportController';
import { authMiddleware } from '@/middlewares/adminAuth';
import { permissionMiddleware } from '@/middlewares/adminAuth';

const exportRouter = express.Router();

exportRouter.get('/classes/excel', authMiddleware, permissionMiddleware('export data'), exportClassesToExcel);
exportRouter.get('/exam-applications/excel', authMiddleware, permissionMiddleware('export data'), exportExamApplicationsToExcel);
exportRouter.get('/students/excel', authMiddleware, permissionMiddleware('export data'), exportStudentsToExcel);

export default exportRouter;
