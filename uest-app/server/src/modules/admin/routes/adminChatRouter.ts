import express from "express";
import {
    getClasses,
    getStudents,
    getConversationBetween,
    getStudentsForClass
} from "../controllers/chatController";
import { authMiddleware } from "@/middlewares/adminAuth";
import { permissionMiddleware } from '@/middlewares/adminAuth';

const router = express.Router();

router.get("/classes", authMiddleware, permissionMiddleware('manage chats'), getClasses);

router.get("/students", authMiddleware, permissionMiddleware('manage chats'), getStudents);

router.get("/students/:classId", authMiddleware, permissionMiddleware('manage chats'), getStudentsForClass);

router.get("/conversation/:classId/:studentId", authMiddleware, permissionMiddleware('manage chats'), getConversationBetween);

export default router;
