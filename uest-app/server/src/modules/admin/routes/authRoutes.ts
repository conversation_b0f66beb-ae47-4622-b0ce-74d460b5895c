import { Router, Request, Response } from 'express';
import { login, logout } from '../controllers/authController';
import { adminLoginSchema } from '../requests/authRequest';
import validateRequest from '@/middlewares/validateRequest';
import { sendMail<PERSON>andler } from '../controllers/emailController';
import { authMiddleware } from '@/middlewares/adminAuth';
import prisma from '@/config/prismaClient';

const authAdminRouter = Router();

authAdminRouter.post('/login', validateRequest(adminLoginSchema), login);
authAdminRouter.post('/send-email', sendMailHandler);
authAdminRouter.post('/logout', logout);

authAdminRouter.get('/me', authMiddleware, async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('Fetching user for ID:', req.user?.id);

    if (!req.user?.id) {
      console.error('No user found in request');
      res.status(401).json({ success: false, message: 'Unauthorized' });
      return;
    }

    const user = await prisma.adminUser.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        role: {
          select: {
            id: true,
            name: true,
            permissions: {
              select: {
                permission: {
                  select: {
                    permissionName: true,
                  },
                },
              },
            },
          },
        },
      },
    }).catch((error: any) => {
      console.error('Prisma error in /me:', {
        message: error.message,
        code: error.code,
        meta: error.meta,
        userId: req.user?.id,
      });
      throw error;
    });

    if (!user) {
      console.error('User not found for ID:', req.user.id);
      res.status(404).json({ success: false, message: 'User not found' });
      return;
    }

    const permissions = user.role?.permissions?.map((p) => p.permission?.permissionName).filter(Boolean) || [];

    res.status(200).json({
      success: true,
      data: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
          ? {
              id: user.role.id,
              name: user.role.name,
              permissions,
            }
          : null,
      },
    });
  } catch (error: any) {
    console.error('Unexpected /me error:', {
      message: error.message,
      stack: error.stack,
      code: error.code,
      meta: error.meta,
      userId: req.user?.id,
    });

    res.status(500).json({
      success: false,
      message: 'Something went wrong!',
      errorDetails: {
        message: error.message,
        code: error.code,
        meta: error.meta,
      },
    });
  } finally {
    await prisma.$disconnect();
  }
});

export default authAdminRouter;