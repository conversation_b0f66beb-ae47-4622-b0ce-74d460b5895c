import express from 'express';
import * as examController from '../controllers/examController';
import { authMiddleware } from '@/middlewares/adminAuth';
import { permissionMiddleware } from '@/middlewares/adminAuth';

const examRouter = express.Router();

examRouter.post('/', authMiddleware, permissionMiddleware('create exam'), examController.createExam);
examRouter.put('/:id', authMiddleware, permissionMiddleware('update exam'), examController.updateExam);
examRouter.get('/', permissionMiddleware('read exam details'), examController.getAllExams);
examRouter.get('/:id', permissionMiddleware('read exam details'), examController.getExamById);
examRouter.delete('/:id', authMiddleware, permissionMiddleware('delete exam'), examController.deleteExam);

export default examRouter;
