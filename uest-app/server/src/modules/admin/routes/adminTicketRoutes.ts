import express from 'express';
import {
  addSMWaterParkEntry,
  getSMWaterParkEntries,
  addShivWaterParkEntry,
  getShivWaterParkEntries
} from '../controllers/adminTicketController';
import { authMiddleware } from '@/middlewares/adminAuth';
import { permissionMiddleware } from '@/middlewares/adminAuth';

const router = express.Router();

router.post('/sm-water-park/add-entry', authMiddleware, permissionMiddleware('manage tickets'), addSMWaterParkEntry);
router.get('/sm-water-park/entries', authMiddleware, permissionMiddleware('manage tickets'), getSMWaterParkEntries);

router.post('/shiv-water-park/add-entry', authMiddleware, permissionMiddleware('manage tickets'), addShivWaterParkEntry);
router.get('/shiv-water-park/entries', authMiddleware, permissionMiddleware('manage tickets'), getShivWaterParkEntries);

export default router;