import prisma from '@/config/prismaClient';
import bcrypt from 'bcrypt';

export async function findUserByEmail(email: string) {
  return prisma.adminUser.findUnique({ where: { email } });
}

export async function comparePassword(plain: string, hash: string) {
  return bcrypt.compare(plain, hash);
}

export async function findUserWithRoleById(id: string) {
  return prisma.adminUser.findUnique({
    where: { id },
    select: {
      id: true,
      name: true,
      email: true,
      role: {
        select: {
          id: true,
          name: true,
          permissions: {
            select: {
              permission: {
                select: {
                  permissionName: true,
                },
              },
            },
          },
        },
      },
    },
  });
}
