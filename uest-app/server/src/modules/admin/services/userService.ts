import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

export const getUsers = async (page: number, pageSize: number) => {
  const skip = (page - 1) * pageSize;
  const [users, total] = await Promise.all([
    prisma.adminUser.findMany({
      skip,
      take: pageSize,
      include: { role: true },
    }),
    prisma.adminUser.count(),
  ]);
  return { users, total };
};

export const getUserById = async (id: string) => {
  return prisma.adminUser.findUnique({
    where: { id },
    include: { role: true },
  });
};

export const createUser = async (data: { name: string; email: string; password: string; roleId: string }) => {
  const hashedPassword = await bcrypt.hash(data.password, 10);
  return prisma.adminUser.create({
    data: {
      id: crypto.randomUUID(),
      name: data.name,
      email: data.email,
      password: hashedPassword,
      roleId: data.roleId,
      createdAt: new Date(),
    },
  });
};

export const updateUser = async (id: string, data: { name: string; email: string; roleId: string }) => {
  return prisma.adminUser.update({
    where: { id },
    data: {
      name: data.name,
      email: data.email,
      roleId: data.roleId,
    },
  });
};

export const deleteUser = async (id: string) => {
  return prisma.adminUser.delete({ where: { id } });
};

export const getRoleName = async (roleId: string) => {
  const role = await prisma.role.findUnique({ where: { id: roleId } });
  return role ? role.name : 'N/A';
};

export const disconnect = async () => {
  await prisma.$disconnect();
};