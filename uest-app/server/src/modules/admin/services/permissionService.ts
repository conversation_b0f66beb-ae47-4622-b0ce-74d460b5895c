import { PrismaClient } from '@prisma/client';
import { PERMISSIONS } from '@/config/constants';

const prisma = new PrismaClient();

export const permissionService = {
  getAllPermissions: async () => {
    try {
      const dbPermissions = await prisma.permission.findMany({
        select: {
          permissionName: true,
        },
      });

      // Group permissions by module using constants.ts
      const groupedPermissions = Object.keys(PERMISSIONS).reduce((acc, module) => {
        acc[module] = Object.keys(PERMISSIONS[module]).reduce((modAcc, perm) => {
          if (dbPermissions.some((dbPerm) => dbPerm.permissionName === perm)) {
            modAcc[perm] = PERMISSIONS[module][perm];
          }
          return modAcc;
        }, {} as { [key: string]: string });
        return acc;
      }, {} as { [key: string]: { [key: string]: string } });

      return groupedPermissions;
    } catch (error) {
      console.error('Error fetching permissions:', error);
      throw new Error('Failed to fetch permissions from database');
    }
  },
};