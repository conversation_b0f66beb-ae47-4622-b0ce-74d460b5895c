import { Router } from 'express';
import {
  getAllClasses,
  getAllClassesforCount,
  getCategoryCountsController,
  getClassDetailsById,
  listApprovedTutors,
  patchClassStatus,
  updateClassByAdmin,
  deleteClass,
  getNearbyClassesController,
} from '../controllers/classesController';
import { authMiddleware, permissionMiddleware } from '@/middlewares/adminAuth';

const classesRoutes = Router();

classesRoutes.get('/details/:id', getClassDetailsById);
classesRoutes.get('/details/:id/admin', authMiddleware, permissionMiddleware('read classes'), getClassDetailsById);
classesRoutes.get('/getAll', authMiddleware, permissionMiddleware('read classes'), getAllClasses);
classesRoutes.get('/getAllDisplayCount', authMiddleware, permissionMiddleware('read classes'), getAllClassesforCount);
classesRoutes.patch('/status/:classId', authMiddleware, permissionMiddleware('update classes'), patchClassStatus);
classesRoutes.put('/admin/:id', authMiddleware, permissionMiddleware('update classes'), updateClassByAdmin);
classesRoutes.delete('/:classId', authMiddleware, permissionMiddleware('delete classes'), deleteClass);
classesRoutes.get('/approved-tutors', listApprovedTutors);
classesRoutes.get('/category-counts', getCategoryCountsController);
classesRoutes.get('/nearby', getNearbyClassesController);

export default classesRoutes;
