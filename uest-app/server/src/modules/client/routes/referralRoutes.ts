import { Router } from 'express';
import {
  generateReferralLink,
  getReferralDashboard,
  getReferralHistoryController,
  getAllReferralLinksController,
  createStaffReferralLinkController,
  deactivateReferralLinkController,
  getReferredUsersController,
  createUwhizEarningController,
  getAllReferralEarningsController,
  updateEarningsPaymentStatusController,
  getReferralLinkEarningsController,
  getOverallEarningsSummaryController,
  getStudentDiscountController,
} from '../controllers/referralController';
import { authClientMiddleware } from '@/middlewares/clientAuth';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';
import { authMiddleware, permissionMiddleware } from '@/middlewares/adminAuth';

const referralRouter = Router();

// Routes for Classes
referralRouter.post('/generate-link', authClientMiddleware, generateReferralLink);
referralRouter.get('/dashboard', authClientMiddleware, getReferralDashboard);
referralRouter.get('/history', authClientMiddleware, getReferralHistoryController);

// Routes for Students
referralRouter.post('/generate-link/student', studentAuthMiddleware, generateReferralLink);
referralRouter.get('/dashboard/student', studentAuthMiddleware, getReferralDashboard);
referralRouter.get('/history/student', studentAuthMiddleware, getReferralHistoryController);
referralRouter.get('/discount/student', studentAuthMiddleware, getStudentDiscountController);

// Admin routes
referralRouter.get('/admin/all-links', authMiddleware, permissionMiddleware('read referrals'), getAllReferralLinksController);
referralRouter.post('/admin/create-staff-link', authMiddleware, permissionMiddleware('create referrals'), createStaffReferralLinkController);
referralRouter.patch('/admin/deactivate/:linkId', authMiddleware, permissionMiddleware('update referrals'), deactivateReferralLinkController);
referralRouter.get('/admin/referred-users/:linkId', authMiddleware, permissionMiddleware('read referrals'), getReferredUsersController);

// Earning management routes
referralRouter.post('/admin/create-uwhiz-earning', authMiddleware, permissionMiddleware('create referrals'), createUwhizEarningController);
referralRouter.get('/admin/earnings', authMiddleware, permissionMiddleware('read referrals'), getAllReferralEarningsController);
referralRouter.get('/admin/earnings-summary', authMiddleware, permissionMiddleware('read referrals'), getOverallEarningsSummaryController);
referralRouter.patch('/admin/earnings/payment-status', authMiddleware, permissionMiddleware('update referrals'), updateEarningsPaymentStatusController);
referralRouter.get('/admin/link-earnings/:linkId', authMiddleware, permissionMiddleware('read referrals'), getReferralLinkEarningsController);

// Public route for U-whiz application earning (called from U-whiz server)
referralRouter.post('/uwhiz-earning', createUwhizEarningController);

export default referralRouter;
