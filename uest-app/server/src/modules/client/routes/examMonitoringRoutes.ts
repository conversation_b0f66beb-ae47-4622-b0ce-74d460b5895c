import { Router } from 'express';
import { uploadExamPhoto, getExamPhotos, getAllExamPhotosController } from '../controllers/examMonitoringController';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';
import { authMiddleware, permissionMiddleware } from '@/middlewares/adminAuth';

const examMonitoringRouter = Router();

examMonitoringRouter.post('/upload-photo', studentAuthMiddleware, uploadExamPhoto);

examMonitoringRouter.get('/photos/:studentId/:examId', authMiddleware, permissionMiddleware('read photo monitoring'), getExamPhotos);

examMonitoringRouter.get('/photos/exam/:examId', authMiddleware, permissionMiddleware('read photo monitoring'), getAllExamPhotosController);

export default examMonitoringRouter;
