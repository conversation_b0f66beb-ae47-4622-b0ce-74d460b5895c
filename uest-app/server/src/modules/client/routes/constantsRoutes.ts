import express, { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { PERMISSIONS } from '@/config/constants';
import { sendSuccess, sendError } from '@/utils/response';
import { permissionMiddleware } from '@/middlewares/adminAuth';

const router = express.Router();
const prisma = new PrismaClient();

router.get('/db-permissions', permissionMiddleware('read role'), async (req: Request, res: Response) => {
  try {
    const dbPermissions = await prisma.permission.findMany({
      select: { permissionName: true },
    });


    if (!dbPermissions.length) {
      sendError(res, 'No permissions found in database', 404);
      return;
    }

    // Group permissions by module using constants.ts
    const groupedPermissions = Object.keys(PERMISSIONS).reduce((acc, module) => {
      acc[module] = Object.keys(PERMISSIONS[module]).reduce((modAcc, perm) => {
        if (dbPermissions.some((dbPerm) => dbPerm.permissionName === perm)) {
          modAcc[perm] = PERMISSIONS[module][perm];
        }
        return modAcc;
      }, {} as { [key: string]: string });
      return acc;
    }, {} as { [key: string]: { [key: string]: string } });

    sendSuccess(res, { permissions: groupedPermissions });
  } catch (error: any) {
    console.error('Error in /db-permissions:', error);
    sendError(res, 'Failed to fetch permissions', 500);
  } finally {
    await prisma.$disconnect();
  }
});

export default router;