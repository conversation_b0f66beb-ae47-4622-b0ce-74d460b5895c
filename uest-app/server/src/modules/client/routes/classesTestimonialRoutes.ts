import express from 'express';
import {
  updateTestimonialStatusHandler,
  getAllTestimonialsHandler,
  getApprovedTestimonialsHandler,
  deleteTestimonialHandler
} from '../controllers/classesTestimonialController';
import { authClientMiddleware } from '@/middlewares/clientAuth';
import { authMiddleware, permissionMiddleware } from '@/middlewares/adminAuth';

const router = express.Router();

router.post('/', authClientMiddleware, createTestimonialHandler);

router.get('/class/:classId', getClassTestimonialsHandler);

router.patch('/:id/status', authMiddleware, permissionMiddleware('update testimonials'), updateTestimonialStatusHandler);

router.get('/', authMiddleware ,getAllTestimonialsHandler);

router.get('/approved', getApprovedTestimonialsHandler);

router.delete('/:id', authClientMiddleware, deleteTestimonialHandler);

router.delete('/admin/:id', authMiddleware, permissionMiddleware('delete testimonials'), deleteTestimonialHandler);

export default router;