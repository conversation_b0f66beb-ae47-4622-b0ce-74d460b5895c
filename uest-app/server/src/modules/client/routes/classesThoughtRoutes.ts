import { Router } from 'express';
import {
  getAllThoughts,
  deleteThought,
  updateThoughtStatus
} from '../controllers/classesThoughtController';
import { authClientMiddleware } from '@/middlewares/clientAuth'
import { authMiddleware, permissionMiddleware } from '@/middlewares/adminAuth';

const thoughtRouter = Router();

thoughtRouter.get('/', getAllThoughts); 
thoughtRouter.get('/:id',authClientMiddleware, getThoughtById);
thoughtRouter.put('/:id', authClientMiddleware, updateThought);
thoughtRouter.patch('/:id/status', authMiddleware, permissionMiddleware('update thoughts'), updateThoughtStatus);
thoughtRouter.delete('/:id',authClientMiddleware, deleteThought);
thoughtRouter.delete('/admin/:id',authMiddleware, permissionMiddleware('delete thoughts'), deleteThought);
 
export default thoughtRouter;