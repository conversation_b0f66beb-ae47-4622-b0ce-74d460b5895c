import express, { Request as ExpressRequest, Response, NextFunction } from "express";
import multer from "multer";
import {
  getAllBlogs,
  getBlogByIdHandler,
  updateBlog,
  updateBlogStatus,
  deleteBlog,
  getApprovedBlogs,
  getMyBlogs,
} from "../../controllers/blogController/blogController";
import validateRequest from "@/middlewares/validateRequest";
import { updateBlogSchema, updateBlogStatusSchema } from "../../requests/blogRequests/blogRequest";
import { authMiddleware, permissionMiddleware } from "@/middlewares/adminAuth";
import { authClientMiddleware } from "@/middlewares/clientAuth";
import { dynamicStorage } from "@/utils/upload";

interface CustomRequest extends ExpressRequest {
  admin?: {
    id: string;
    [key: string]: any;
  };
  class?: {
    id: string;
    [key: string]: any;
  };
}

const asRequestHandler = (handler: any) => handler as express.RequestHandler;

const blogRouter = express.Router();

const uploadBlogImage = multer(
  dynamicStorage({ folder: "blogs", classIdKey: "classId" })
);

blogRouter.get("/my-blogs", authClientMiddleware, asRequestHandler(getMyBlogs));
blogRouter.get("/approved", asRequestHandler(getApprovedBlogs));
blogRouter.get("/", authMiddleware, permissionMiddleware('read blog'), asRequestHandler(getAllBlogs));
blogRouter.get("/:id", asRequestHandler(getBlogByIdHandler));

blogRouter.put(
  "/:id",
  authClientMiddleware,
  uploadBlogImage.single("blogImage"),
  validateRequest(updateBlogSchema),
  asRequestHandler(updateBlog)
);

blogRouter.patch(
  "/:id/status",
  authMiddleware,
  permissionMiddleware('update blog'),
  validateRequest(updateBlogStatusSchema),
  asRequestHandler(updateBlogStatus)
);

const combinedAuthMiddleware = (req: CustomRequest, res: Response, next: NextFunction) => {
  authMiddleware(req as any, res, (err?: any) => {
    if (!err && req.admin) {
      return next();
    }
    authClientMiddleware(req as any, res, next);
  });
};

blogRouter.delete("/:id", asRequestHandler(combinedAuthMiddleware), permissionMiddleware('delete blog'), asRequestHandler(deleteBlog));

export default blogRouter;
