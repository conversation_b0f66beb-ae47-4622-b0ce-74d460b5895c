import { Request, Response, NextFunction } from 'express';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { sendError } from '@/utils/response';
import prisma from '@/config/prismaClient';

interface JwtPayload {
  id: string;
  email: string;
}

export function permissionMiddleware(permission: string) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const token = req.cookies.admin_jwt;

    if (!token) {
      console.error('No token provided for permission:', permission);
      sendError(res, 'Unauthorized: No token provided', 401);
      return;
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'secret123') as JwtPayload;
      console.log('Verifying permission:', permission, 'for user ID:', decoded.id);
      const user = await prisma.adminUser.findUnique({
        where: { id: decoded.id },
        include: {
          role: {
            include: {
              permissions: {
                include: { permission: true },
              },
            },
          },
        },
      }).catch((error: any) => {
        console.error('Prisma error in permissionMiddleware:', {
          message: error.message,
          code: error.code,
          meta: error.meta,
          userId: decoded.id,
        });
        throw error;
      });

      if (!user) {
        console.error('User not found for ID:', decoded.id);
        sendError(res, 'Unauthorized: User not found', 401);
        return;
      }

      if (user.email === '<EMAIL>') {
        console.log('Bypassing permission <NAME_EMAIL>');
        req.user = user;
        return next();
      }

      if (!user.role) {
        console.error('No role assigned to user:', user.email);
        sendError(res, 'Unauthorized: No role assigned', 403);
        return;
      }

      const hasPermission = user.role.permissions.some(
        (p) => p.permission.permissionName === permission
      );

      if (!hasPermission) {
        console.error(`User ${user.email} lacks permission: ${permission}`);
        sendError(res, `Forbidden: Missing permission ${permission}`, 403);
        return;
      }

      console.log(`User ${user.email} has permission: ${permission}`);
      req.user = user;
      next();
    } catch (err: any) {
      console.error('Token verification error:', {
        message: err.message,
        stack: err.stack,
        token,
      });
      sendError(res, 'Unauthorized: Invalid token', 401);
    }
  };
}

export async function authMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  const token = req.cookies.admin_jwt;

  if (!token) {
    console.error('No token provided for /me');
    sendError(res, 'Unauthorized: No token provided', 401);
    return;
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'secret123') as JwtPayload;
    console.log('Decoded JWT for /me:', { id: decoded.id, email: decoded.email });
    const user = await prisma.adminUser.findUnique({
      where: { id: decoded.id },
      include: {
        role: {
          include: {
            permissions: {
              include: { permission: true },
            },
          },
        },
      },
    }).catch((error: any) => {
      console.error('Prisma error in authMiddleware:', {
        message: error.message,
        code: error.code,
        meta: error.meta,
        userId: decoded.id,
      });
      throw error;
    });

    if (!user) {
      console.error('User not found for ID:', decoded.id);
      sendError(res, 'Unauthorized: User not found', 401);
      return;
    }

    req.user = user;
    next();
  } catch (err: any) {
    console.error('Token verification error for /me:', {
      message: err.message,
      stack: err.stack,
      token,
    });
    sendError(res, 'Unauthorized: Invalid token', 401);
  }
}