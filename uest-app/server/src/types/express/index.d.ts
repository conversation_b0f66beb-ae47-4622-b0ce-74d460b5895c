export {};

declare global {
  namespace Express {
    interface Request {
      class?: Record<string, any>;
    }
  }
}

declare global {
  namespace Express {
    interface Request {
      student?: {
        id: string;
        contact: string;
      };
    }
  }
}

declare global {
  namespace Express {
    interface Request {
      class?: Record<string, any>;
      student?: {
        id: string;
        email: string;
      };
      user?: {
        id: string;
        name?: string | null;
        email: string;
        role?: {
          id: string;
          name: string;
          permissions: {
            id: string;
            roleId: string;
            permissionId: string;
            permission: {
              id: string;
              permissionName: string;
              createdAt: Date;
            };
          }[];
        } | null;
      };
    }
  }
}