export interface PermissionModule {
  [key: string]: string;
}

export interface Permissions {
  [key: string]: PermissionModule;
}

export const PERMISSIONS: Permissions = {
  BLOG_MANAGEMENT: {
    'create blog': 'Add blogs',
    'read blog': 'View blogs',
    'update blog': 'Edit blog status',
    'delete blog': 'Delete blogs',
    'export blog data': 'Export blog data',
  },
  TESTIMONIALS: {
    'create testimonials': 'Add testimonials',
    'read testimonials': 'View testimonials',
    'update testimonials': 'Edit testimonials',
    'delete testimonials': 'Delete testimonials',
    'export testimonials data': 'Export testimonials data',
  },
  THOUGHTS: {
    'create thoughts': 'Add thoughts',
    'read thoughts': 'View thoughts',
    'update thoughts': 'Edit thoughts',
    'delete thoughts': 'Delete thoughts',
    'export thoughts data': 'Export thoughts data',
  },
  STUDENT_MANAGEMENT: {
    'create student details': 'Add student details',
    'read student details': 'View student details',
    'update student details': 'Edit student details',
    'delete student details': 'Delete student details',
    'read reviews': 'View reviews',
    'update reviews': 'Edit reviews',
    'delete reviews': 'Delete reviews',
    'export student data': 'Export student data',
  },
  EXAM_MANAGEMENT: {
    'create exam': 'Create exams',
    'read exam details': 'View exam details',
    'update exam': 'Update exams',
    'delete exam': 'Delete exams',
    'create question bank': 'Add questions',
    'read question bank': 'View question bank',
    'update question bank': 'Edit questions',
    'delete question bank': 'Delete questions',
    'read photo monitoring': 'View photo monitoring',
    'update photo monitoring': 'Manage photo monitoring',
    'read exam applications': 'View exam applications',
    'update exam applications': 'Process exam applications',
    'export exam data': 'Export exam data',
  },
  REFERRALS: {
    'create referrals': 'Add referrals',
    'read referrals': 'View referrals',
    'update referrals': 'Edit referrals',
    'delete referrals': 'Delete referrals',
    'export referrals data': 'Export referrals data',
  },
  CHATS: {
    'create chats': 'Start conversations',
    'read chats': 'View chats',
    'update chats': 'Edit chat messages',
    'delete chats': 'Delete chat conversations',
    'manage chats': 'Manage chat conversations',
    'export chats data': 'Export chats data',
  },
  ROLE_MANAGEMENT: {
    'create role': 'Create roles',
    'read role': 'View roles',
    'update role': 'Update roles',
    'delete role': 'Delete roles',
    'export role data': 'Export role data',
  },
  USER_MANAGEMENT: {
    'create users': 'Create users',
    'read users': 'View users',
    'update users': 'Update users',
    'delete users': 'Delete users',
    'export users data': 'Export users data',
  },
  CLASSES_MANAGEMENT: {
    'create classes': 'Add classes',
    'read classes': 'View classes',
    'update classes': 'Edit classes',
    'delete classes': 'Delete classes',
    'export classes data': 'Export classes data',
  },
  EXPORT_MANAGEMENT: {
    'export data': 'Export data to Excel',
  },
  TICKET_MANAGEMENT: {
    'create tickets': 'Create tickets',
    'read tickets': 'View tickets',
    'update tickets': 'Update tickets',
    'delete tickets': 'Delete tickets',
    'manage tickets': 'Manage water park tickets',
    'export tickets data': 'Export tickets data',
  },
};

export const STATUS = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
};