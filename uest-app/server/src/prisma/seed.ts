import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import { PERMISSIONS } from '../config/constants';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting database seeding...');

  // First, seed all permissions
  console.log('Seeding permissions...');
  const allPermissions: string[] = [];
  for (const module of Object.keys(PERMISSIONS)) {
    const permissions = PERMISSIONS[module];
    for (const permissionName of Object.keys(permissions)) {
      try {
        await prisma.permission.upsert({
          where: { permissionName },
          update: {},
          create: {
            id: crypto.randomUUID(),
            permissionName,
            createdAt: new Date(),
          },
        });
        allPermissions.push(permissionName);
      } catch (error) {
        console.error(`Failed to seed permission ${permissionName}:`, error);
      }
    }
  }
  console.log(`Seeded ${allPermissions.length} permissions`);

  // Create admin role with all permissions
  console.log('Creating admin role...');
  const adminRole = await prisma.role.upsert({
    where: { name: 'Super Admin' },
    update: {},
    create: {
      id: crypto.randomUUID(),
      name: 'Super Admin',
      createdAt: new Date(),
    },
  });

  // Assign all permissions to admin role
  console.log('Assigning permissions to admin role...');
  for (const permissionName of allPermissions) {
    try {
      await prisma.roleHasPermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: adminRole.id,
            permissionId: (await prisma.permission.findUnique({ where: { permissionName } }))!.id,
          },
        },
        update: {},
        create: {
          id: crypto.randomUUID(),
          roleId: adminRole.id,
          permissionId: (await prisma.permission.findUnique({ where: { permissionName } }))!.id,
        },
      });
    } catch (error) {
      console.error(`Failed to assign permission ${permissionName} to admin role:`, error);
    }
  }

  // Create admin user with role
  console.log('Creating admin user...');
  const hashedPassword = await bcrypt.hash('admin@123', 10);

  const admin = await prisma.adminUser.upsert({
    where: { email: '<EMAIL>' },
    update: {
      roleId: adminRole.id,
    },
    create: {
      id: crypto.randomUUID(),
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Super Admin',
      roleId: adminRole.id,
      createdAt: new Date(),
    },
  });

  console.log('Admin user created:', admin);
  console.log('Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
