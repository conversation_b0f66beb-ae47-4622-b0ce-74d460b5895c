import axiosInstance from '../config/axios';

export interface StoreItem {
  id: string;
  name: string;
  description: string;
  coinPrice: number;
  quantity: number;
  category: string;
  image: string | null;
  status: 'ACTIVE' | 'INACTIVE';
  createdAt: string;
  updatedAt: string;
}

export interface StoreFilters {
  category?: string;
  status?: string;
  search?: string;
}

export interface StoreStats {
  totalItems: number;
  activeItems: number;
  inactiveItems: number;
  outOfStockItems: number;
  categoriesCount: number;
  categories: Array<{
    category: string;
    count: number;
  }>;
}

export interface UserCoins {
  coins: number;
}

export async function getAllStoreItems(filters?: StoreFilters) {
  try {
    const params = new URLSearchParams();
        params.append('status', 'ACTIVE');
    if (filters?.category) {
      params.append('category', filters.category);
    }
    if (filters?.search) {
      params.append('search', filters.search);
    }

    const response = await axiosInstance.get(`/admin/store?${params.toString()}`);
    return {
      success: true,
      data: response.data.data as StoreItem[]
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store items'
    };
  }
}

export async function getStoreItemById(id: string) {
  try {
    const response = await axiosInstance.get(`/admin/store/${id}`);
    return {
      success: true,
      data: response.data.data as StoreItem
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store item'
    };
  }
}

export async function getStoreStats() {
  try {
    const response = await axiosInstance.get('/admin/store/stats');
    return {
      success: true,
      data: response.data.data as StoreStats
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store statistics'
    };
  }
}

export async function getUserCoins() {
  try {
    const response = await axiosInstance.get('/coins/get-total-coins/student');
    return {
      success: true,
      data: response.data as UserCoins
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch user coins'
    };
  }
}

export async function getStoreCategories() {
  try {
    const storeItemsResult = await getAllStoreItems();
    if (!storeItemsResult.success) {
      return storeItemsResult;
    }

    const categories = Array.from(
      new Set(storeItemsResult.data.map(item => item.category))
    ).map(category => ({
      id: category.toLowerCase(),
      name: category.charAt(0).toUpperCase() + category.slice(1),
      count: storeItemsResult.data.filter(item => item.category === category).length
    }));

    return {
      success: true,
      data: categories
    };
  } catch (error: any) {
    return {
      success: false,
      error: 'Failed to fetch store categories'
    };
  }
}
